import reactRefresh from 'eslint-plugin-react-refresh';
import { config as reactInternalConfig } from './react-internal.js';

/**
 * A custom ESLint configuration for Refine applications.
 *
 * @type {import("eslint").Linter.Config[]}
 */
export const config = [
  ...reactInternalConfig,
  {
    plugins: {
      'react-refresh': reactRefresh,
    },
    rules: {
      'react/prop-types': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      'react-refresh/only-export-components': ['warn', { allowConstantExport: true }],
    },
  },
];
