{"name": "@ps/common", "version": "1.0.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["./dist/**"], "publishConfig": {"access": "public"}, "typesVersions": {"*": {"*": ["src/*"]}}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}, "./*": {"import": "./dist/*.js", "require": "./dist/*.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch"}, "devDependencies": {"tsup": "^8.5.0", "typescript": "^5.8.3"}}