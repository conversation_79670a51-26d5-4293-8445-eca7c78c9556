export const getSocialAuthUrlWithRedirect = (
  platform: 'google' | 'facebook',
  { redirectUrl, clientId, state }: { redirectUrl: string; clientId: string; state?: string },
): string => {
  switch (platform) {
    case 'facebook':
      return `https://www.facebook.com/v2.10/dialog/oauth?client_id=${clientId}&scope=email&redirect_uri=${redirectUrl}&${state}`;
    case 'google':
      return `https://accounts.google.com/o/oauth2/v2/auth?redirect_uri=${redirectUrl}&prompt=consent&response_type=code&client_id=${clientId}&scope=https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile&access_type=offline&${state}`;
    default:
      return '';
  }
};
