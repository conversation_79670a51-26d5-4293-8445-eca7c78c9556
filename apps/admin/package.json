{"name": "@ps/admin", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "refine dev", "build": "tsc && refine build", "start": "refine start", "lint": "eslint . --max-warnings 0", "refine": "refine"}, "dependencies": {"@refinedev/cli": "^2.16.46", "@refinedev/core": "^4.57.9", "@refinedev/devtools": "^1.2.16", "@refinedev/kbar": "^1.3.16", "@refinedev/nestjsx-crud": "^5.0.12", "@refinedev/react-router": "^1.0.1", "axios": "^1.10.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.6.2"}, "devDependencies": {"@ps/eslint-config": "workspace:*", "@ps/typescript-config": "workspace:*", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "typescript": "^5.8.3", "vite-plugin-mkcert": "^1.17.8", "vite": "^6.3.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "refine": {"projectId": "Uhag6h-tUlwdk-2c6wRT"}}