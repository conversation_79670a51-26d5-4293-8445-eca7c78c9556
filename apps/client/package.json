{"name": "@ps/client", "version": "0.0.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000 --experimental-https", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@ps/common": "workspace:*", "@ps/ui": "workspace:*", "js-cookie": "^3.0.5", "lucide-react": "^0.516.0", "next": "^15.3.3", "next-auth": "^5.0.0-beta.28", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "zod": "^3.25.67"}, "devDependencies": {"@ps/eslint-config": "workspace:*", "@ps/types": "workspace:*", "@ps/typescript-config": "workspace:*", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.29.0", "typescript": "^5.8.3"}}