'use client';

import type { User } from '@ps/types';

import { createContext, useContext } from 'react';

export interface AppContextType {
  isAuthenticated: boolean;
  user?: User;
}

const AppContext = createContext<AppContextType>({
  isAuthenticated: false,
});

const useAppContext = () => {
  const context = useContext(AppContext);

  if (context === undefined) {
    throw new Error('useAppContext must be used within a AppProvider');
  }

  return context;
};

export default useAppContext;
export { AppContext };
