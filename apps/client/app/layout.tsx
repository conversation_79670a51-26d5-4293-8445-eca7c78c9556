import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';

import { Providers } from '@/components/providers';
import { cn } from '@ps/ui/lib/utils';

import '@ps/ui/globals.css';

const fontSans = Geist({
  subsets: ['latin'],
  variable: '--font-sans',
});

const fontMono = Geist_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(fontSans.variable, fontMono.variable, 'font-sans antialiased')}>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
