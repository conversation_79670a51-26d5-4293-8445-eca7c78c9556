const features = [
  {
    title: 'Rich Text Editor',
    description: 'Write with our powerful WYSIWYG editor with markdown support.',
  },
  {
    title: 'SEO Optimized',
    description: 'Built-in SEO tools to help your content rank higher in search results.',
  },
  {
    title: 'Analytics Dashboard',
    description: "Track your blog's performance with detailed analytics and insights.",
  },
  {
    title: 'Custom Themes',
    description: 'Choose from beautiful themes or create your own custom design.',
  },
];

export default function HomeFeatures() {
  return (
    <section id="features" className="py-20 bg-muted/50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            Everything you need to blog
          </h2>
          <p className="mt-4 text-lg text-muted-foreground">
            Powerful features to help you create and grow your blog
          </p>
        </div>

        <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
          {features.map((feature) => (
            <div key={feature.title} className="bg-background p-6 rounded-lg shadow-sm">
              <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
              <p className="text-muted-foreground">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
