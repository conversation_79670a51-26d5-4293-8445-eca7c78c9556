'use server';

import { isRedirectError } from 'next/dist/client/components/redirect-error';

import { signIn } from '@/auth';
import API from '@/services/api';

import { SignupSchema } from '../utils/validation';
import { AuthError } from '../utils';

export const signup = async (
  values: Partial<SignupSchema> & {
    token?: string;
    provider?: 'custom'; // (custom -> for social logins via redirect)
    redirect?: boolean;
  },
  callbackUrl?: string | null,
): Promise<{ message?: string; csrfToken?: string }> => {
  try {
    // First create the user
    await API.post('/auth/signup', values);

    // Then sign them in
    await signIn('credentials', {
      email: values.email,
      password: values.password,
      redirectTo: callbackUrl || '/',
    });

    return { message: '' };
  } catch (error) {
    if (isRedirectError(error)) {
      throw error; // Next.js will handle this
    }
    const err = error as AuthError;
    if (error instanceof AuthError) {
      const errorObj = JSON.parse(err?.message || '{}');
      return { ...errorObj };
    }
    return { message: err?.message || 'Something went wrong!' };
  }
};
