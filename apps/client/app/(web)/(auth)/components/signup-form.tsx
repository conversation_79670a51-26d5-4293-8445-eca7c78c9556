'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { getSocialAuthUrlWithRedirect } from '@ps/common/utils/auth';
import { CardContent, Card } from '@ps/ui/components/card';
import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';
import { Label } from '@ps/ui/components/label';
import { cn } from '@ps/ui/lib/utils';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@ps/ui/components/form';

import placeholder from '@img/placeholder.svg';
// import { signup } from '../actions/signup';
import { signupSchema, type SignupSchema } from '../utils/validation';
import API from '@/services/api';
import { login } from '../actions/login';

export function SignupForm({ className, ...props }: React.ComponentProps<'div'>) {
  // const router = useRouter();
  const form = useForm<SignupSchema>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
    },
  });

  const onSubmit = async (values: SignupSchema) => {
    // const result = await signup(values);
    // if (result.message) {
    //   form.setError('root', { message: result.message });
    // }
    API.post<SignupSchema, { accessToken: string; message?: string }>('/auth/signup', values).then(
      async ({ accessToken, message }) => {
        if (accessToken) await login({ token: accessToken, provider: 'custom' }, '/payment');
        if (message) {
          if (message === 'Email Already exist.') {
            form.setError('email', { message });
          } else {
            form.setError('root', { message });
          }
        }
      },
    );
  };

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <Card className="overflow-hidden p-0">
        <CardContent className="grid p-0 md:grid-cols-2">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="p-6 md:p-8">
              <div className="flex flex-col gap-6">
                <div className="flex flex-col items-center text-center">
                  <h1 className="text-2xl font-bold">Welcome</h1>
                  <p className="text-muted-foreground text-balance">
                    Sign up for your Acme Inc account
                  </p>
                </div>

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="John Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input type="password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {form.formState.errors.root && (
                  <p className="text-destructive text-sm text-center">
                    {form.formState.errors.root.message}
                  </p>
                )}

                <Button type="submit" className="w-full" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting ? 'Creating Account...' : 'Create Account'}
                </Button>

                <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
                  <span className="bg-card text-muted-foreground relative z-10 px-2">
                    Or continue with
                  </span>
                </div>

                <Link
                  href={getSocialAuthUrlWithRedirect('google', {
                    redirectUrl: '/dashboard',
                    clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
                  })}
                >
                  <Button variant="outline" type="button" className="w-full">
                    {/* <GoogleIcon /> */}
                    Sign up with Google
                  </Button>
                </Link>

                <div className="text-center text-sm">
                  Already have an account?{' '}
                  <Link href="/login" className="underline underline-offset-4">
                    Login
                  </Link>
                </div>
              </div>
            </form>
          </Form>
          <div className="bg-muted relative hidden md:block">
            <Image
              className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
              src={placeholder}
              alt="Image"
            />
          </div>
        </CardContent>
      </Card>
      <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
        By clicking continue, you agree to our <a href="#">Terms of Service</a> and{' '}
        <a href="#">Privacy Policy</a>.
      </div>
    </div>
  );
}
