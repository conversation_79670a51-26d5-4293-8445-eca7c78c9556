import type { User } from '@ps/types';
import type { NextAuthConfig, NextAuthResult } from 'next-auth';

import { cookies } from 'next/headers';
import Credentials from 'next-auth/providers/credentials';
import NextAuth from 'next-auth';

import { MILLISECONDS_IN } from '@ps/common/constants/time';
import { AuthError } from '@/app/(web)/(auth)/utils';
import API from '@/services/api';

// Reference https://authjs.dev/getting-started/migrating-to-v5

const authConfig: NextAuthConfig = {
  basePath: '/api/auth',
  providers: [
    Credentials({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'text', placeholder: '<EMAIL>' },
        password: { label: 'Password', type: 'password' },
        provider: { label: 'provider', type: 'text' }, // Typescript type for custom provider
        token: { label: 'token', type: 'text' }, // Typescript type for custom provider
      },
      // @ts-expect-error Todo
      async authorize({ callbackUrl: _, ...credentials }) {
        if (credentials?.provider === 'custom' && credentials?.token) {
          (await cookies()).set('token', String(credentials?.token));
          return { id: 1, token: credentials?.token };
        }

        const { accessToken: token, message } = await API.post<
          Partial<Record<'email' | 'password', unknown>>,
          { accessToken: string; message?: string }
        >('/auth/login', credentials).catch((err) => ({ message: err, accessToken: null }));

        if (message) throw new AuthError(JSON.stringify({ message }));

        if (token) {
          (await cookies()).set('token', token);
          return { id: 1, token };
        } else {
          return null;
        }
      },
    }),
  ],
  callbacks: {
    jwt: async ({ token, user }) => {
      if (user) {
        token.jwt = user.token;
        token.user = { id: user._id };
      }
      return token;
    },
    session: async ({ session, token }) => {
      if (token?.jwt) {
        session.token = String(token.jwt);
        const user = await API.get<User>('/me', {
          headers: { Authorization: `Bearer ${token.jwt}` },
        });
        // @ts-expect-error Todo
        session.user = user;
      }
      return session;
    },
  },
  events: {
    async signIn(data) {
      (await cookies()).set('token', data?.user?.token);
    },
    async signOut() {
      (await cookies()).delete('token');
    },
  },
  pages: {
    signIn: '/login',
  },
  session: {
    strategy: 'jwt',
    maxAge: MILLISECONDS_IN.TWENTY_FOUR_HOURS,
  },
  jwt: {
    maxAge: MILLISECONDS_IN.TWENTY_FOUR_HOURS,
  },
};

const result = NextAuth(authConfig);
export const handlers: NextAuthResult['handlers'] = result.handlers;
export const signOut: NextAuthResult['signOut'] = result.signOut;
export const signIn: NextAuthResult['signIn'] = result.signIn;
export const auth: NextAuthResult['auth'] = result.auth;
