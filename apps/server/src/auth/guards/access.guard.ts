import type { ExecutionContext, CanActivate } from '@nestjs/common';
import type { Reflector } from '@nestjs/core';
import type { UserRole } from '@/resources/user/user.enums';
import type { User } from '@/resources/user/user.model';

import { SetMetadata, Injectable } from '@nestjs/common';

@Injectable()
export class AccessGuard implements CanActivate {
  private readonly privilegeMap: Record<UserRole, number> = {
    superadmin: 3,
    owner: 2,
    admin: 2,
    user: 1,
  };

  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.get<UserRole[]>('roles', context.getHandler());
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const user = context.switchToHttp().getRequest<Request>().user as User;
    if (!user || !user.role || !user.team) {
      return false;
    }

    const userPrivilege = this.privilegeMap[user.role];
    const requiredPrivilege = Math.max(...requiredRoles.map((role) => this.privilegeMap[role]));
    return userPrivilege >= requiredPrivilege;
  }
}

export const Roles = (...roles: string[]) => SetMetadata('roles', roles);
