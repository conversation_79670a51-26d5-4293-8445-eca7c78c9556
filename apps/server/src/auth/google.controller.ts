import type { GoogleSignupDto } from './dto/signup.dto';
import type { User } from '@/resources/user/user.model';

import { ForbiddenException, Controller, UseGuards, Redirect, Get, Req } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

import { UserService } from '@/resources/user/user.service';
import { UserRole } from '@/resources/user/user.enums';
import { ReqUser } from '@/common/decorators/req-user.decorator';
import config from '@/common/configs/config';

import { AuthService } from './auth.service';

@Controller('auth/google')
export class GoogleController {
  constructor(
    private readonly userService: UserService,
    private readonly authService: AuthService,
  ) {}

  @Get('callback')
  @UseGuards(AuthGuard('google'))
  @Redirect()
  async callback(@Req() req: Request, @ReqUser() user: User) {
    const state = (typeof req.query.state === 'string' ? JSON.parse(req.query.state) : {}) as {
      app: string;
    };
    const isAdmin = state.app === 'admin';
    const redirectBase = config().app[isAdmin ? 'admin' : 'client'].url;

    try {
      const { name, email, googleId, profilePicture, verified } = user;
      const existingUser = await this.userService.findOne({ email });

      if (!existingUser) {
        const createdUser = await this.authService.signup<GoogleSignupDto>(
          { name, email, googleId, profilePicture, verified },
          'google',
        );

        const { accessToken } = await this.authService.login(createdUser);
        return {
          url: `${redirectBase}/auth/google/callback?token=${accessToken}&redirectTo=/payment`,
        };
      }

      if (isAdmin && existingUser.role !== UserRole.SuperAdmin) {
        throw new ForbiddenException();
      }

      await this.userService.update(String(existingUser._id), user);
      const { accessToken } = await this.authService.login(existingUser);
      return { url: `${redirectBase}/auth/google/callback?token=${accessToken}` };
    } catch (error) {
      const errorMessage =
        (error as { code?: number })?.code === 11000
          ? 'Email already exist.'
          : (error as Error)?.message;

      return { url: `${redirectBase}/auth/google/callback?error=${errorMessage}` };
    }
  }
}
