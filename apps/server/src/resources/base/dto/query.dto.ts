import type { <PERSON><PERSON>Q<PERSON>y } from '../base.types';

import { IsOptional, IsN<PERSON>ber, IsString, IsArray } from 'class-validator';
import { ApiHideProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class QueryDto implements CrudQuery {
  @ApiPropertyOptional({ description: 'List of joins to populate.' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  join?: string[];

  @ApiPropertyOptional({
    description: "Sorting options in the format of ['field,ASC or DESC'].",
    default: ['createAt,DESC'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  sort?: string[];

  @ApiPropertyOptional({ description: 'Number of results per page', default: 10 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Page number', default: 1 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number = 1;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  s?: string;
}
