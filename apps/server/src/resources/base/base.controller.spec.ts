import type { INestApplication } from '@nestjs/common';
import type { TestingModule } from '@nestjs/testing';
import type { CrudQuery } from './base.types';

import { MongooseModule } from '@nestjs/mongoose';
import { ValidationPipe } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ObjectId } from 'mongodb';
import { Test } from '@nestjs/testing';
import request from 'supertest';
import qs from 'querystring';

import { Teams, Users } from '@/test/seeds';
import { AccessGuard } from '@/auth/guards/access.guard';
import { CacheModule } from '@/modules/cache/cache.module';
import { MailService } from '@/modules/mail/mail.service';
import { UserService } from '@/resources/user/user.service';
import { AuthModule } from '@/auth/auth.module';
import { UserModule } from '@/resources/user/user.module';
import config from '@/common/configs/config';

class MockMailService {
  addToMailQueue(_: any) {}
}

describe('BaseController', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let userService: UserService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(config().mongo.url),
        CacheModule.forRoot(),
        AuthModule,
        UserModule,
      ],
    })
      .useMocker((token) => {
        if (token === MailService) {
          return new MockMailService();
        }
      })
      .overrideGuard(AccessGuard)
      .useValue({ canActivate: () => true })
      .compile();

    userService = module.get<UserService>(UserService);
    jwtService = module.get<JwtService>(JwtService);

    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ transform: true, whitelist: true }));
    await app.init();
  });

  afterAll(() => app.close());

  function getAuthToken(userId: ObjectId): string {
    const token = jwtService.sign({ sub: userId.toString() }, { secret: config().jwt.secret });
    return `Bearer ${token}`;
  }

  it('API: GET /resource', () => {
    const query: CrudQuery = {
      limit: 4,
      page: 1,
      sort: ['name,ASC', 'email,DESC'],
    };
    const queryString = qs.stringify(query as qs.ParsedUrlQueryInput);

    return request(app.getHttpServer())
      .get(`/users?${queryString}`)
      .set({ Authorization: getAuthToken(Users.SuperAdmin), 'x-app-name': 'admin' })
      .expect(200)
      .expect(({ body }) => {
        expect(Array.isArray(body.data)).toBeTruthy();
        expect(body.data.length).toBe(4);
        expect(Number.isInteger(body.total)).toBeTruthy();
      });
  });

  it('API: GET /resource with Owner user', () => {
    return request(app.getHttpServer())
      .get(`/users`)
      .set('Authorization', getAuthToken(Users.Owner))
      .expect(200)
      .expect(({ body }) => {
        expect(Array.isArray(body.data)).toBeTruthy();
        expect(body.data.length).toBe(1);
        expect(Number.isInteger(body.total)).toBeTruthy();
        expect(body.data[0].name).toBe('John Doe');
      });
  });

  it('API: GET /resource/:id', () =>
    request(app.getHttpServer())
      .get(`/users/${Users.User.toString()}`)
      .set({ Authorization: getAuthToken(Users.SuperAdmin), 'x-app-name': 'admin' })
      .expect(200)
      .expect(({ body }) => {
        expect(body._id).toBe(Users.User.toString());
      }));

  it('API: GET /resource/:id [NOT FOUND]', () =>
    request(app.getHttpServer())
      .get(`/users/${new ObjectId()}`)
      .set({ Authorization: getAuthToken(Users.SuperAdmin), 'x-app-name': 'admin' })
      .expect(404));

  it('API: POST /resource', () =>
    request(app.getHttpServer())
      .post('/users')
      .send({
        name: 'John New',
        email: '<EMAIL>',
        password: 'password123',
        team: Teams.Omega.toString(),
      })
      .set({ Authorization: getAuthToken(Users.SuperAdmin), 'x-app-name': 'admin' })
      .expect(201)
      .expect(({ body }) => {
        expect(body.email).toBe('<EMAIL>');
      }));

  it('API: PUT /resource/:id', async () => {
    const id = (await userService.findOne({ email: '<EMAIL>' }))?._id;
    return request(app.getHttpServer())
      .patch(`/users/${id?.toString()}`)
      .send({ email: '<EMAIL>' })
      .set({ Authorization: getAuthToken(Users.SuperAdmin), 'x-app-name': 'admin' })
      .expect(200)
      .expect(({ body }) => {
        expect(body.email).toBe('<EMAIL>');
      });
  });

  it('API: DELETE /resource/:id', async () => {
    const id = (await userService.findOne({ email: '<EMAIL>' }))?._id;
    const r = request(app.getHttpServer())
      .delete(`/users/${id?.toString()}`)
      .set({ Authorization: getAuthToken(Users.SuperAdmin), 'x-app-name': 'admin' })
      .expect(200)
      .expect(async ({ body }) => {
        expect(body.success).toBeTruthy();
      });

    await userService.destroy(id as ObjectId);
    return r;
  });
});
