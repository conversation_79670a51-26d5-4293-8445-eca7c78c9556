import type { ControllerOptions } from '@/resources/base/base.types';

import { Controller } from '@nestjs/common';

import { UserRole } from '@/resources/user/user.enums';

import { BaseController } from '../base/base.controller';
import { TeamService } from './team.service';
import { Team } from './team.model';

const options: Partial<ControllerOptions> = {
  isTeamResource: false,
  list: { roles: [UserRole.SuperAdmin] },
  show: { roles: [UserRole.SuperAdmin] },
  create: { enabled: false },
  update: { roles: [UserRole.SuperAdmin] },
  remove: { enabled: false },
};

@Controller('teams')
export class TeamController extends BaseController<Team, typeof TeamService, null, null>({
  name: 'Team',
  path: 'teams',
  service: TeamService,
  options,
}) {}
