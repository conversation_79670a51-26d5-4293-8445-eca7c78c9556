import { BullBoardModule } from '@bull-board/nestjs';
import { Global, Module } from '@nestjs/common';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';

import config from '@/common/configs/config';

import { MailPreviewService } from './mail-preview.service';
import { MailchimpService } from './mailchimp/mailchimp.service';
import { MailQueueModule } from './mail-queue.module';
import { MAIL_QUEUE_NAME } from './mail.constants';
import { MailController } from './mail.controller';
import { MailService } from './mail.service';

@Global()
@Module({
  imports: [
    BullBoardModule.forFeature({ name: MAIL_QUEUE_NAME, adapter: BullMQAdapter }),
    MailQueueModule,
  ],
  controllers: [MailController],
  providers: [
    {
      provide: MailService,
      useClass: config().isDev ? MailPreviewService : MailchimpService,
    },
    MailchimpService,
  ],
  exports: [MailService],
})
export class MailModule {}
