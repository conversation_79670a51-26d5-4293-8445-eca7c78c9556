import type { MailPayload } from './mail.types';

import { NotFoundException, Controller, Head<PERSON>, <PERSON>m, Get } from '@nestjs/common';

import { getEmailData, TEMPLATES } from '@/modules/mail/mail.utils';
import config from '@/common/configs/config';

const BASE_STYLES = `
  <style>
    body {
      justify-content: center;
      align-items: flex-start;
      display: flex;
    }
    table {
      font-family: Helvetica, sans-serif;
      border-collapse: collapse;
      max-width: 600px;
      font-size: 14px;
      width: 100%;
    }

    th, td {
      border: 1px solid #ddd;
      text-align: left;
      padding: 8px;
    }

    tr:nth-child(even){
      background-color: #f2f2f2;
    }

    th {
      background-color: #FEAF6C;
      font-size: 16px;
      color: white;
    }
  </style>
`;

const DUMMY_CONTEXT = {
  ctaUrl: `${config().app.client.url}/reset-password/dummy-reset-id`,
  inviter: '<PERSON>',
};

const DUMMY_MAIL_PAYLOADS: MailPayload = {
  name: '<PERSON>',
  to: '<EMAIL>',
  subject: 'Text Email',
  context: DUMMY_CONTEXT,
  template: '',
};

@Controller('mails')
export class MailController {
  @Get()
  @Header('content-type', 'text/html')
  getMailLists() {
    const templateRows: string[] = [];

    for (const template of TEMPLATES) {
      const url = `${config().app.client.url}/mails/${template}`;
      templateRows.push(
        `<tr>
            <td style="text-transform: capitalize">${template.replace(/-/g, ' ')}</td>
            <td><a href="${url}">Email</a></td>
        </tr>`,
      );
    }

    return `
      ${BASE_STYLES}
      <body>
        <table>
          <tr>
            <th>Name</th>
            <th>Email Template</th>
          </tr>
          ${templateRows.join('')}
        </table>
      </body>
    `;
  }

  @Get(':template')
  @Header('content-type', 'text/html')
  async getMail(@Param('template') template: string) {
    try {
      const { html } = await getEmailData({ ...DUMMY_MAIL_PAYLOADS, template });
      return html;
    } catch (error) {
      throw new NotFoundException(`Template not found. ${(error as Error)?.message}`);
    }
  }
}
