/* eslint-disable @typescript-eslint/no-redundant-type-constituents */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-call */
import type { CreateCheckoutSessionDto } from './dto/create-checkout-session.dto';
import type { Stripe } from 'stripe';
import type { Team } from '@/resources/team/team.model';
import type { User } from '@/resources/user/user.model';

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import StripeLib from 'stripe';

import { SubscriptionStatus, SubscriptionPlan, BillingPeriod } from '@/resources/team/team.enums';
import { TeamService } from '@/resources/team/team.service';
import config from '@/common/configs/config';

import { STRIPE_PRICES } from './stripe.constants';

interface SubscriptionWithDetails extends Stripe.Subscription {
  current_period_start: number;
  current_period_end: number;
  trial_end: number | null;
  cancel_at_period_end: boolean;
}

interface InvoiceWithSubscription extends Stripe.Invoice {
  subscription: string | Stripe.Subscription | null;
}

@Injectable()
export class StripeService {
  private readonly logger = new Logger(StripeService.name);
  private readonly stripe: StripeLib;

  constructor(private readonly teamService: TeamService) {
    this.stripe = new StripeLib(config().stripe.secretKey, {
      apiVersion: '2025-05-28.basil',
    });
  }

  async activateFreePlan(user: User): Promise<{ success: boolean }> {
    const team = user.team as Team;

    if (team.stripeSubscriptionId) {
      void this.cancelSubscription(user);
    }

    await this.teamService.update(team._id, {
      subscriptionStatus: SubscriptionStatus.Active,
      subscriptionStartDate: new Date(),
      subscriptionPlan: SubscriptionPlan.Free,
      billingPeriod: BillingPeriod.Lifetime,
      stripeSubscriptionId: null,
      cancelAtPeriodEnd: false,
    });

    return { success: true };
  }

  async createCheckoutSession(
    user: User,
    { plan, billingPeriod }: CreateCheckoutSessionDto,
  ): Promise<Stripe.Checkout.Session> {
    const team = user.team as Team;
    const stripeCustomerId = await this.getOrCreateCustomer(team, user);

    const priceId = STRIPE_PRICES[plan][billingPeriod];

    const session = await this.stripe.checkout.sessions.create({
      customer: stripeCustomerId,
      payment_method_types: ['card'],
      line_items: [{ price: priceId, quantity: 1 }],
      mode: billingPeriod === BillingPeriod.Lifetime ? 'payment' : 'subscription',
      success_url: `${config().app.server.url}/integrations/stripe/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${config().app.client.url}/settings/billing?canceled=true`,
      allow_promotion_codes: true,
      metadata: {
        teamId: team._id.toString(),
        userId: user._id.toString(),
        email: user.email,
        billingPeriod,
        plan,
        oldSubscriptionId: team.stripeSubscriptionId || '',
        isReplacement: team.stripeSubscriptionId ? 'true' : 'false',
      },
    });

    return session;
  }

  async cancelSubscription(user: User): Promise<void> {
    const team = user.team as Team;

    if (!team.stripeSubscriptionId) {
      throw new Error('No active subscription found');
    }

    await this.stripe.subscriptions.update(team.stripeSubscriptionId, {
      cancel_at_period_end: true,
    });

    await this.teamService.update(team._id, {
      cancelAtPeriodEnd: true,
    });
  }

  async handleCheckoutSuccess(
    sessionId: string,
  ): Promise<{ redirectUrl: string; plan: string; billingPeriod: string }> {
    const session = await this.stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['line_items', 'subscription', 'invoice.subscription'],
    });

    if (!session) {
      throw new Error('Checkout session not found');
    }

    const { plan, billingPeriod } = session.metadata as {
      plan: SubscriptionPlan;
      billingPeriod: BillingPeriod;
    };

    await this.handleCheckoutSessionCompleted(session);

    if (session.subscription) {
      await this.handleSubscriptionUpdated(session.subscription as Stripe.Subscription);
    }

    return {
      redirectUrl: `${config().app.client.url}/settings/billing`,
      billingPeriod,
      plan,
    };
  }

  verifyWebhookSignature(payload: Buffer, signature: string): Stripe.Event {
    return this.stripe.webhooks.constructEvent(payload, signature, config().stripe.webhookSecret);
  }

  async handleWebhookEvent(event: Stripe.Event): Promise<void> {
    this.logger.log(`Processing stripe webhook event: ${event.type}`);

    switch (event.type) {
      case 'checkout.session.completed':
        await this.handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session);
        break;
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        break;
      case 'customer.subscription.deleted':
        await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
        break;
      case 'invoice.payment_succeeded':
        await this.handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;
      case 'invoice.payment_failed':
        await this.handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
        break;
      default:
        this.logger.warn(`Unhandled event type: ${event.type}`);
    }
  }

  private async getOrCreateCustomer(team: Team, user: User): Promise<string> {
    if (team.stripeCustomerId) {
      return team.stripeCustomerId;
    }

    const customer = await this.stripe.customers.create({
      name: user.name,
      email: user.email,
      metadata: {
        teamId: team._id.toString(),
      },
    });

    await this.teamService.update(team._id, {
      stripeCustomerId: customer.id,
    });

    return customer.id;
  }

  private async handleCheckoutSessionCompleted(session: Stripe.Checkout.Session): Promise<void> {
    const { teamId, plan, billingPeriod, oldSubscriptionId, isReplacement } = session.metadata as {
      teamId: string;
      plan: SubscriptionPlan;
      billingPeriod: BillingPeriod;
      oldSubscriptionId: string;
      isReplacement: string;
    };

    if (!teamId || !plan) {
      this.logger.error('Missing teamId or plan in session metadata');
      return;
    }

    // For one-time lifetime subscription payments
    if (billingPeriod === BillingPeriod.Lifetime && session.payment_status === 'paid') {
      // Cancel old subscription if this is a replacement
      if (isReplacement === 'true' && oldSubscriptionId) {
        try {
          await this.stripe.subscriptions.cancel(oldSubscriptionId);
          this.logger.log(
            `Canceled old subscription ${oldSubscriptionId} after successful lifetime purchase`,
          );
        } catch (error) {
          this.logger.error(`Failed to cancel old subscription: ${(error as Error).message}`);
        }
      }

      await this.teamService.update(teamId, {
        subscriptionStatus: SubscriptionStatus.Active,
        subscriptionStartDate: new Date(),
        subscriptionPlan: plan,
        billingPeriod: BillingPeriod.Lifetime,
        stripeSubscriptionId: null,
        cancelAtPeriodEnd: false,
      });
    } else if (session.subscription) {
      // For regular subscriptions, we'll handle in the subscription events
      // But we can cancel the old subscription now if this is a replacement
      if (
        isReplacement === 'true' &&
        oldSubscriptionId &&
        typeof session.subscription === 'object' &&
        session.subscription &&
        oldSubscriptionId !== session.subscription.id
      ) {
        try {
          await this.stripe.subscriptions.cancel(oldSubscriptionId);
          this.logger.log(
            `Canceled old subscription ${oldSubscriptionId} after new subscription ${session.subscription.id} created`,
          );
        } catch (error) {
          this.logger.error(`Failed to cancel old subscription: ${(error as Error).message}`);
        }
      }
    }
  }

  private async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    const team = await this.findTeamByCustomerId(subscription.customer as string);
    if (!team) return;

    // Get the plan from the product ID
    const firstItem = subscription.items.data[0];
    if (!firstItem) {
      this.logger.error(`Subscription ${subscription.id} has no items.`);
      return;
    }
    const productId = firstItem.price.product as string;
    const product = await this.stripe.products.retrieve(productId);
    const plan = product.metadata.plan;

    if (!plan) {
      throw new BadRequestException('Missing plan in product metadata.');
    }

    // Get the billing period from the price
    const priceId = firstItem.price.id;
    const price = await this.stripe.prices.retrieve(priceId);
    const billingPeriod =
      price.metadata.billingPeriod ||
      (price.recurring?.interval === 'month' ? BillingPeriod.Monthly : BillingPeriod.Yearly);

    await this.teamService.update(team._id, {
      billingPeriod: billingPeriod as BillingPeriod,
      subscriptionPlan: plan as SubscriptionPlan,
      subscriptionStatus: subscription.status as SubscriptionStatus,
      subscriptionStartDate: new Date(
        (subscription as SubscriptionWithDetails).current_period_start * 1000,
      ),
      subscriptionEndDate: new Date(
        (subscription as SubscriptionWithDetails).current_period_end * 1000,
      ),
      stripeSubscriptionId: subscription.id,
      trialEndDate: (subscription as SubscriptionWithDetails).trial_end
        ? new Date(((subscription as SubscriptionWithDetails).trial_end as number) * 1000)
        : undefined,
      cancelAtPeriodEnd: (subscription as SubscriptionWithDetails).cancel_at_period_end,
    });
  }

  private async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    const team = await this.findTeamByCustomerId(subscription.customer as string);
    if (!team) return;

    await this.teamService.update(team._id, {
      subscriptionPlan: SubscriptionPlan.Free,
      subscriptionStatus: SubscriptionStatus.Canceled,
      cancelAtPeriodEnd: false,
    });
  }

  private async handleInvoicePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    const team = await this.findTeamByCustomerId(invoice.customer as string);
    if (!team) return;

    if ((invoice as InvoiceWithSubscription).subscription) {
      await this.teamService.update(team._id, {
        subscriptionStatus: SubscriptionStatus.Active,
      });
    }
  }

  private async handleInvoicePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    const team = await this.findTeamByCustomerId(invoice.customer as string);
    if (!team) return;

    if ((invoice as InvoiceWithSubscription).subscription) {
      await this.teamService.update(team._id, {
        subscriptionStatus: SubscriptionStatus.PastDue,
      });
    }
  }

  private async findTeamByCustomerId(customerId: string): Promise<Team | null> {
    return this.teamService.findOne({ stripeCustomerId: customerId });
  }
}
