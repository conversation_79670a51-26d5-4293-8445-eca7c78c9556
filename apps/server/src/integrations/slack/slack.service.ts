import type {
  ChatPostMessageArguments,
  ChatPostMessageResponse,
  ChatUpdateArguments,
  ChatUpdateResponse,
  KnownBlock,
  Block,
} from '@slack/web-api';
import type { SlackChannelName } from './slack.types';

import { Injectable, Logger } from '@nestjs/common';
import { WebClient } from '@slack/web-api';

import config from '@/common/configs/config';

import { SLACK_CHANNELS, SLACK_AVATAR } from './slack.constants';
import { sanitizedBlocks } from './slack.utils';

const icon_url = SLACK_AVATAR;

@Injectable()
export class SlackService {
  private readonly logger = new Logger(this.constructor.name);
  private readonly slack: WebClient;

  constructor() {
    this.slack = new WebClient(config().slack.botToken);
  }

  async sendMessage({
    channel,
    blocks,
    ...params
  }: {
    channel: SlackChannelName;
    blocks?: (Block | KnownBlock)[];
    attachments?: { text: string; color: string }[];
  } & Omit<ChatPostMessageArguments, 'channel'>): Promise<ChatPostMessageResponse> {
    if (!config().slack.botToken || !config().isProd) {
      return Promise.resolve({} as ChatPostMessageResponse);
    }

    try {
      return await this.slack.chat.postMessage({
        ...params,
        channel: SLACK_CHANNELS[channel].toUpperCase(),
        blocks: sanitizedBlocks(blocks),
        username: config().app.name,
        unfurl_links: false,
        icon_url,
      } as ChatPostMessageArguments);
    } catch (e) {
      this.logger.warn('Slack Send Message Failed:', e);
      return Promise.resolve({} as ChatPostMessageResponse);
    }
  }

  async updateMessage({
    channel,
    blocks,
    ...params
  }: {
    channel: SlackChannelName;
    blocks?: (Block | KnownBlock)[];
    attachments?: { text: string; color: string }[];
  } & Omit<ChatUpdateArguments, 'channel'>): Promise<ChatUpdateResponse> {
    if (!config().slack.botToken || !config().isProd) {
      return Promise.resolve({} as ChatUpdateResponse);
    }

    try {
      return await this.slack.chat.update({
        ...params,
        channel: SLACK_CHANNELS[channel].toUpperCase(),
        blocks: sanitizedBlocks(blocks),
        username: config().app.name,
        unfurl_links: false,
        icon_url,
      } as ChatUpdateArguments);
    } catch (e) {
      this.logger.warn('Slack Update Message Failed', e);
      return Promise.resolve({} as ChatUpdateResponse);
    }
  }

  async sendSignupAlert(
    { name, email }: { name: string; email: string },
    method: 'local' | 'google',
  ) {
    const attachments: { text: string; color: string }[] = [];

    if (method === 'google') {
      attachments.push({
        text: `Signed up using Google`,
        color: '#4285F4',
      });
    }

    await this.sendMessage({
      text: `*${email}* *${name}* has signed up :tada:`,
      channel: 'demo',
      attachments,
    });
  }
}
