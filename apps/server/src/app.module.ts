import { Lo<PERSON>, Modu<PERSON> } from '@nestjs/common';

import { ThrottlerModule } from '@nestjs/throttler';
import { BullBoardModule } from '@bull-board/nestjs';
import { ExpressAdapter } from '@bull-board/express';
import { MongooseModule } from '@nestjs/mongoose';
import { BullModule } from '@nestjs/bullmq';
import basicAuth from 'express-basic-auth';

import { StripeModule } from '@/integrations/stripe/stripe.module';
import { CacheModule } from '@/modules/cache/cache.module';
import { AuthModule } from '@/auth/auth.module';
import { MailModule } from '@/modules/mail/mail.module';
import { MeModule } from '@/me/me.module';
import config from '@/common/configs/config';

// Resources
import { TeamModule } from '@/resources/team/team.module';
import { UserModule } from '@/resources/user/user.module';

import { AppController } from './app.controller';
import { AppService } from './app.service';

const boardOptions = {
  uiConfig: {
    boardTitle: config().app.name,
    favIcon: {
      alternative: `${config().app.client.url}/favicon.ico`,
      default: `${config().app.client.url}/favicon.ico`,
    },
    boardLogo: {
      path: `${config().app.client.url}/images/${config().app.name?.toLowerCase()}.svg`,
    },
  },
};

@Module({
  imports: [
    MongooseModule.forRoot(config().mongo.url, {
      onConnectionCreate: () => new Logger('Mongoose').log('Connected to MongoDB.'),
    }),
    CacheModule.forRoot(),
    ThrottlerModule.forRoot({ throttlers: [{ ttl: 60000, limit: 10 }] }),
    BullModule.forRoot({ connection: config().redis }),
    BullBoardModule.forRoot({
      route: '/bull-board',
      adapter: ExpressAdapter,
      middleware: basicAuth({
        challenge: true,
        users: { [config().bullBoard.username]: config().bullBoard.password },
      }),
      boardOptions,
    }),
    // Resources
    TeamModule,
    UserModule,
    // Common Modules
    AuthModule,
    MailModule,
    // Integrations
    StripeModule,
    // Others
    MeModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
