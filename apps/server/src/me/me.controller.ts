import type { User as UserType } from '@/resources/user/user.model';

import { UseG<PERSON><PERSON>, Controller, Get } from '@nestjs/common';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { Auth } from '@/auth/guards/auth.guard';

@Controller('me')
export class MeController {
  @UseGuards(Auth)
  @Get()
  getAuthenticatedUser(@ReqUser() user: UserType) {
    return user;
  }
}
