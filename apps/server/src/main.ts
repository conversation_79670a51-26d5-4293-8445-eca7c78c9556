import type { NestExpressApplication } from '@nestjs/platform-express';
import type { NestApplicationOptions } from '@nestjs/common';

import { ValidationPipe, Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import compression from 'compression';
import * as path from 'path';
import * as fs from 'fs';

import { generateOpenAPISpec } from '@/open-api-docs';
import { AppModule } from '@/app.module';
import config from '@/common/configs/config';

const getHttpsOptions = (): NestApplicationOptions => {
  if (!config().nest.https) return {};

  const projectRoot = path.resolve(process.cwd(), '../../');
  const certsPath = path.join(projectRoot, 'certs');

  const keyPath = path.join(certsPath, 'localhost-key.pem');
  const certPath = path.join(certsPath, 'localhost.pem');

  if (config().isDev && (!fs.existsSync(keyPath) || !fs.existsSync(certPath))) {
    console.error(
      '\u001b[31m \u274C Certificate files (localhost-key.pem and localhost.pem) not found in the certs directory. Please install the certificate files before running the server in the development environment. \u001b[0m',
    );
    return process.exit(1);
  }

  const httpsOptions = config().isDev
    ? {
        httpsOptions: {
          key: fs.readFileSync(path.join(certsPath, 'localhost-key.pem')),
          cert: fs.readFileSync(path.join(certsPath, 'localhost.pem')),
        },
      }
    : {};

  return httpsOptions;
};

async function bootstrap(): Promise<void> {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    ...getHttpsOptions(),
    rawBody: true,
  });

  // Validation
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // Middlewares
  app.useBodyParser('json', { limit: '50mb' });
  app.useBodyParser('urlencoded', { extended: true, limit: '50mb' });
  app.use(compression());

  app.enableShutdownHooks();

  // Swagger
  generateOpenAPISpec(app);

  // CORS
  const domainName = config().app.server.url.replace('https://', '');
  if (config().nest.cors.enabled) {
    app.enableCors({
      origin: [
        new RegExp(`^https://([a-zA-Z0-9-]+\\.)*${domainName.replace('.', '\\.')}$`),
        /.*localhost.*/,
        /127\.0\.0\.1/,
        /192.168/,
      ],
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
      credentials: true,
      maxAge: 86400,
    });
  }

  await app.listen(config().nest.port);

  new Logger('NestApplication').log(
    `✔️  ${config().app.name} server is running on: http${config().nest.https ? 's' : ''}://localhost:${config().nest.port}/`,
  );
}

void bootstrap();
