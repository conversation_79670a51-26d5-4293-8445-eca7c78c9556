export interface Config {
  app: {
    name: string;
    server: App;
    client: App;
    admin: App;
    image: App;
    mails: {
      support: string;
    };
  };
  nest: {
    https: boolean;
    port: number;
    cors: {
      enabled: boolean;
    };
  };

  // Environment
  environment: 'production' | 'staging' | 'development' | 'test';
  isProd: boolean;
  isStaging: boolean;
  isTest: boolean;
  isDev: boolean;

  // Data Sources
  mongo: {
    url: string;
  };
  redis: {
    url: string;
    host: string;
    port: number;
    username?: string;
    password?: string;
  };

  // Tools
  swagger: SwaggerConfig;
  bullBoard: {
    username: string;
    password: string;
  };

  // Integrations
  google: Integration;
  stripe: {
    secretKey: string;
    publishableKey: string;
    webhookSecret: string;
  };
  slack: { botToken: string };
  mailchimp: { apiKey: string };

  // Security
  jwt: {
    secret: string;
    expiresIn: string;
    refreshIn: string;
    bcryptSaltOrRound: number;
  };
  blockedIPAddresses: string[];
  blockedCountries: string[];
  blockedDomains: string[];
}

interface App {
  name?: string;
  url: string;
}

interface Integration {
  clientId: string;
  clientSecret: string;
}

interface SwaggerConfig {
  enabled: boolean;
  favIcon: string;
  docs: {
    title: string;
    description: string;
    version: string;
    path: string;
    pageTitle: string;
    pathNameFilter?: string;
    username: string;
    password: string;
  }[];
}
