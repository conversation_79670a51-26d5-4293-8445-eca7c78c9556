import { Config } from './config.types';

const getEnv = () => (process.env.NODE_ENV as Config['environment']) || 'development';

const getRedisConfig = () => {
  let redisUrl = process.env.REDIS_URL;

  if (!redisUrl) {
    throw new Error('REDIS_URL environment variable is missing.');
  }

  if (!redisUrl.includes('@')) {
    redisUrl = redisUrl.replace('redis://', 'redis://:@');
  }
  const redisUrlSplit = redisUrl.split(':');

  return {
    url: redisUrl,
    host: redisUrlSplit[2]?.split('@')[1] ?? 'localhost',
    port: parseInt(redisUrlSplit[3] || '6379', 10),
    username: redisUrlSplit[1]?.replace('//', '') || undefined,
    password: redisUrlSplit[2]?.split('@')[0] || undefined,
  };
};

const appName = process.env.PRODUCT_NAME || 'Base';
const clientUrl = process.env.CLIENT_URL || 'http://localhost:3000';
const domain = clientUrl.replace(/https?:\/\//, '');
const config: Config = {
  app: {
    name: appName,
    server: { url: process.env.SERVER_URL || 'http://localhost:3333' },
    client: { url: clientUrl },
    admin: { url: process.env.ADMIN_URL || 'http://localhost:30001' },
    image: { url: `https://images.${domain}` },
    mails: {
      support: '<EMAIL>',
    },
  },
  nest: {
    https: process.env.HTTPS === 'true',
    port: parseInt(process.env.PORT || '3333', 10) || 3333,
    cors: {
      enabled: true,
    },
  },

  // Environment
  environment: getEnv() || 'development',
  isProd: getEnv() === 'production',
  isStaging: getEnv() === 'staging',
  isDev: getEnv() === 'development',
  isTest: getEnv() === 'test',

  // Data Sources
  mongo: {
    url: process.env.MONGO_URL || 'mongodb://localhost:27017/base',
  },
  redis: getRedisConfig(),

  // Tools
  swagger: {
    enabled: true,
    favIcon: `${clientUrl}/favicon.ico`,
    docs: [
      {
        title: `${appName} APIs`,
        description: 'API Documentation',
        version: '1.0',
        path: 'docs',
        pageTitle: `${appName} API`,
        username: process.env.OPENAPI_USERNAME || '',
        password: process.env.OPENAPI_PASSWORD || '',
      },
    ],
  },
  bullBoard: {
    username: process.env.BULL_BOARD_USERNAME || '',
    password: process.env.BULL_BOARD_PASSWORD || '',
  },

  // Integrations
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID || '',
    clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
  },
  stripe: {
    secretKey: process.env.STRIPE_SECRET_KEY || '',
    publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
  },
  slack: { botToken: process.env.SLACK_BOT_TOKEN || '' },
  mailchimp: { apiKey: process.env.MAILCHIMP_API_KEY || '' },

  // Security
  jwt: {
    secret: process.env.JWT_SECRET || '',
    expiresIn: '14d',
    refreshIn: '30d',
    bcryptSaltOrRound: 10,
  },
  blockedIPAddresses: (process.env.BLOCKED_IP_ADDRESSES || '').split(','),
  blockedCountries: (process.env.BLOCKED_COUNTRIES || '').split(','),
  blockedDomains: [
    '@bikerider.com',
    '@counsellor.com',
    '@gardener.com',
    '@engineer.com',
    '@doglover.com',
    '@saxpads.xyz',
    '@artlover.com',
    '@comic.com',
    '@hilarious.com',
    '@minister.com',
    '@politician.com',
    '@mail.com',
    '@gmx.com',
  ],
};

export default (): Config => config;
