{"name": "@ps/base", "private": true, "scripts": {"build": "dotenv -- turbo run build", "dev": "dotenv -- turbo run dev", "lint": "dotenv -- turbo run lint", "test": "dotenv -e .env.test -- turbo run test", "test:watch": "dotenv -e .env.test -- turbo run test:watch", "format": "prettier --write \"**/*.{js,ts,tsx,md,json}\"", "check-types": "turbo run check-types"}, "devDependencies": {"dotenv-cli": "^8.0.0", "prettier": "^3.5.3", "turbo": "^2.5.4", "typescript": "5.8.3"}, "engines": {"node": ">=22"}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}